/**
 * 行程数据模型
 * 包含行程相关的类型定义和数据管理
 */

// 行程状态枚举
export enum TripStatus {
  UPCOMING = 'upcoming',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed'
}

// 行程类型枚举
export enum TripType {
  BUSINESS = 'business',
  LEISURE = 'leisure',
  FAMILY = 'family'
}

// 行程接口定义
export interface Trip {
  id: number;
  title: string;
  destination: string;
  startDate: string;
  endDate: string;
  daysCount: number;
  progress: number;
  status: string;
  tripType: string;
  imageUrl?: string;
}

// 行程统计接口
export interface TripStats {
  total: number;
  upcoming: number;
  inProgress: number;
  completed: number;
}

// 活动项目接口
export interface Activity {
  id: number;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  location?: string;
  type: ActivityType;
  completed: boolean;
}

// 活动查询结果接口
export interface ActivityQueryResult {
  activity: Activity | null;
  dailyItinerary: DailyItinerary | null;
}

// 活动类型枚举
export enum ActivityType {
  SIGHTSEEING = 'sightseeing',
  DINING = 'dining',
  SHOPPING = 'shopping',
  TRANSPORTATION = 'transportation',
  ACCOMMODATION = 'accommodation',
  ENTERTAINMENT = 'entertainment',
  OTHER = 'other'
}

// 每日行程接口
export interface DailyItinerary {
  date: string;
  dayNumber: number;
  title: string;
  activities: Activity[];
}

// 快速操作类型枚举
export enum QuickActionType {
  DAILY_ITINERARY = 'daily_itinerary',
  TRAVEL_MEMORIES = 'travel_memories',
  RESTAURANT_BOOKING = 'restaurant_booking',
  PACKING_LIST = 'packing_list',
  EXPENSE_MANAGEMENT = 'expense_management',
  ADD_ACTIVITY = 'add_activity'
}

// 快速操作接口
export interface QuickAction {
  id: string;
  title: string;
  icon: string;
  type: QuickActionType;
  enabled: boolean;
}

// 示例数据
export const SAMPLE_TRIPS: Trip[] = [
  {
    id: 1,
    title: '巴黎浪漫之旅',
    destination: '法国巴黎',
    startDate: '2024-07-15',
    endDate: '2024-07-22',
    daysCount: 8,
    progress: 75,
    status: TripStatus.UPCOMING,
    tripType: TripType.LEISURE
  },
  {
    id: 2,
    title: '东京文化探索',
    destination: '日本东京',
    startDate: '2024-08-10',
    endDate: '2024-08-17',
    daysCount: 8,
    progress: 30,
    status: TripStatus.UPCOMING,
    tripType: TripType.LEISURE
  },
  {
    id: 3,
    title: '纽约商务之行',
    destination: '美国纽约',
    startDate: '2024-05-15',
    endDate: '2024-05-20',
    daysCount: 6,
    progress: 100,
    status: TripStatus.COMPLETED,
    tripType: TripType.BUSINESS
  },
  {
    id: 4,
    title: '三亚家庭度假',
    destination: '中国三亚',
    startDate: '2024-04-01',
    endDate: '2024-04-05',
    daysCount: 5,
    progress: 100,
    status: TripStatus.COMPLETED,
    tripType: TripType.FAMILY
  },
  {
    id: 5,
    title: '上海商务会议',
    destination: '中国上海',
    startDate: '2024-06-26',
    endDate: '2024-06-30',
    daysCount: 5,
    progress: 60,
    status: TripStatus.IN_PROGRESS,
    tripType: TripType.BUSINESS
  },
  {
    id: 6,
    title: '欧洲深度游',
    destination: '欧洲多国',
    startDate: '2024-10-10',
    endDate: '2024-10-19',
    daysCount: 10,
    progress: 5,
    status: TripStatus.UPCOMING,
    tripType: TripType.LEISURE
  },
  {
    id: 7,
    title: '北京家庭游',
    destination: '中国北京',
    startDate: '2024-04-01',
    endDate: '2024-04-05',
    daysCount: 5,
    progress: 100,
    status: TripStatus.COMPLETED,
    tripType: TripType.FAMILY
  },
  {
    id: 8,
    title: '南京杭州游',
    destination: '中国南京',
    startDate: '2024-07-06',
    endDate: '2024-07-07',
    daysCount: 2,
    progress: 0,
    status: TripStatus.UPCOMING,
    tripType: TripType.LEISURE
  }
];

// 行程详情映射接口
interface TripDetailsMap {
  getTripDetails(tripId: number): DailyItinerary[];
}

// 示例行程详情数据
class TripDetailsData implements TripDetailsMap {
  private trip1Details: DailyItinerary[] = [
    {
      date: '2024-07-15',
      dayNumber: 1,
      title: '抵达巴黎',
      activities: [
        {
          id: 1,
          title: '抵达戴高乐机场',
          description: '航班号: AF123',
          startTime: '14:30',
          endTime: '15:00',
          location: '戴高乐机场',
          type: ActivityType.TRANSPORTATION,
          completed: false
        },
        {
          id: 2,
          title: '前往酒店',
          description: '乘坐机场快线',
          startTime: '15:30',
          endTime: '16:30',
          location: '香榭丽舍大街',
          type: ActivityType.TRANSPORTATION,
          completed: false
        },
        {
          id: 3,
          title: '酒店入住',
          description: '巴黎香榭丽舍酒店',
          startTime: '17:00',
          endTime: '18:00',
          location: '香榭丽舍大街',
          type: ActivityType.ACCOMMODATION,
          completed: false
        },
        {
          id: 4,
          title: '晚餐',
          description: '法式料理',
          startTime: '19:30',
          endTime: '21:00',
          location: '酒店附近餐厅',
          type: ActivityType.DINING,
          completed: false
        }
      ]
    },
    {
      date: '2024-07-16',
      dayNumber: 2,
      title: '经典巴黎游',
      activities: [
        {
          id: 5,
          title: '参观埃菲尔铁塔',
          description: '登塔观景',
          startTime: '09:00',
          endTime: '11:30',
          location: '埃菲尔铁塔',
          type: ActivityType.SIGHTSEEING,
          completed: false
        },
        {
          id: 6,
          title: '塞纳河游船',
          description: '欣赏两岸风光',
          startTime: '14:00',
          endTime: '15:30',
          location: '塞纳河',
          type: ActivityType.SIGHTSEEING,
          completed: false
        },
        {
          id: 7,
          title: '卢浮宫参观',
          description: '艺术珍品欣赏',
          startTime: '16:00',
          endTime: '18:00',
          location: '卢浮宫',
          type: ActivityType.SIGHTSEEING,
          completed: false
        }
      ]
    }
  ];

  getTripDetails(tripId: number): DailyItinerary[] {
    if (tripId === 1) {
      return this.trip1Details;
    }
    return [];
  }
}

export const SAMPLE_TRIP_DETAILS = new TripDetailsData();

// 快速操作配置
export const QUICK_ACTIONS: QuickAction[] = [
  {
    id: 'daily_itinerary',
    title: '每日行程',
    icon: '📅',
    type: QuickActionType.DAILY_ITINERARY,
    enabled: true
  },
  {
    id: 'travel_memories',
    title: '旅行回忆',
    icon: '📷',
    type: QuickActionType.TRAVEL_MEMORIES,
    enabled: true
  },
  {
    id: 'restaurant_booking',
    title: '预订管理',
    icon: '🍽️',
    type: QuickActionType.RESTAURANT_BOOKING,
    enabled: true
  },
  {
    id: 'packing_list',
    title: '打包清单',
    icon: '🧳',
    type: QuickActionType.PACKING_LIST,
    enabled: true
  },
  {
    id: 'expense_management',
    title: '费用管理',
    icon: '💰',
    type: QuickActionType.EXPENSE_MANAGEMENT,
    enabled: true
  },
  {
    id: 'add_activity',
    title: '添加活动',
    icon: '➕',
    type: QuickActionType.ADD_ACTIVITY,
    enabled: true
  }
];

// 行程数据管理类
export class TripDataManager {
  private trips: Trip[] = SAMPLE_TRIPS;

  // 获取所有行程
  getAllTrips(): Trip[] {
    return this.trips;
  }

  // 根据ID获取行程
  getTripById(id: number): Trip | undefined {
    return this.trips.find(trip => trip.id === id);
  }

  // 添加新行程
  addTrip(trip: Trip): void {
    this.trips.push(trip);
  }

  // 更新行程
  updateTrip(id: number, updatedTrip: Partial<Trip>): boolean {
    const index = this.trips.findIndex(trip => trip.id === id);
    if (index !== -1) {
      // 手动更新属性而不使用展开运算符
      const currentTrip = this.trips[index];
      if (updatedTrip.title !== undefined) currentTrip.title = updatedTrip.title;
      if (updatedTrip.destination !== undefined) currentTrip.destination = updatedTrip.destination;
      if (updatedTrip.startDate !== undefined) currentTrip.startDate = updatedTrip.startDate;
      if (updatedTrip.endDate !== undefined) currentTrip.endDate = updatedTrip.endDate;
      if (updatedTrip.daysCount !== undefined) currentTrip.daysCount = updatedTrip.daysCount;
      if (updatedTrip.progress !== undefined) currentTrip.progress = updatedTrip.progress;
      if (updatedTrip.status !== undefined) currentTrip.status = updatedTrip.status;
      if (updatedTrip.tripType !== undefined) currentTrip.tripType = updatedTrip.tripType;
      return true;
    }
    return false;
  }

  // 删除行程
  deleteTrip(id: number): boolean {
    const index = this.trips.findIndex(trip => trip.id === id);
    if (index !== -1) {
      this.trips.splice(index, 1);
      return true;
    }
    return false;
  }

  // 按状态筛选行程
  getTripsByStatus(status: string): Trip[] {
    if (status === 'all') {
      return this.trips;
    }
    return this.trips.filter(trip => trip.status === status);
  }

  // 搜索行程
  searchTrips(keyword: string): Trip[] {
    if (!keyword) {
      return this.trips;
    }
    const lowerKeyword = keyword.toLowerCase();
    return this.trips.filter(trip =>
      trip.title.toLowerCase().includes(lowerKeyword) ||
      trip.destination.toLowerCase().includes(lowerKeyword)
    );
  }

  // 获取行程统计信息
  getTripStats(): TripStats {
    const stats: TripStats = {
      total: this.trips.length,
      upcoming: this.trips.filter(trip => trip.status === TripStatus.UPCOMING).length,
      inProgress: this.trips.filter(trip => trip.status === TripStatus.IN_PROGRESS).length,
      completed: this.trips.filter(trip => trip.status === TripStatus.COMPLETED).length
    };
    return stats;
  }

  // 获取行程详情
  getTripDetails(tripId: number): DailyItinerary[] {
    return SAMPLE_TRIP_DETAILS.getTripDetails(tripId);
  }

  // 获取快速操作列表
  getQuickActions(): QuickAction[] {
    return QUICK_ACTIONS;
  }

  // 更新活动完成状态
  updateActivityStatus(tripId: number, activityId: number, completed: boolean): void {
    const details = SAMPLE_TRIP_DETAILS.getTripDetails(tripId);
    if (details) {
      for (const day of details) {
        const activity = day.activities.find(a => a.id === activityId);
        if (activity) {
          activity.completed = completed;
          break;
        }
      }
    }
  }

  // 获取单个活动详情
  getActivityById(tripId: number, activityId: number): ActivityQueryResult {
    const details = SAMPLE_TRIP_DETAILS.getTripDetails(tripId);
    for (const day of details) {
      const activity = day.activities.find(a => a.id === activityId);
      if (activity) {
        const result: ActivityQueryResult = {
          activity: activity,
          dailyItinerary: day
        };
        return result;
      }
    }
    const emptyResult: ActivityQueryResult = {
      activity: null,
      dailyItinerary: null
    };
    return emptyResult;
  }

  // 更新活动信息
  updateActivity(tripId: number, activityId: number, updatedActivity: Partial<Activity>): boolean {
    const details = SAMPLE_TRIP_DETAILS.getTripDetails(tripId);
    for (const day of details) {
      const activity = day.activities.find(a => a.id === activityId);
      if (activity) {
        // 手动更新属性
        if (updatedActivity.title !== undefined) activity.title = updatedActivity.title;
        if (updatedActivity.description !== undefined) activity.description = updatedActivity.description;
        if (updatedActivity.startTime !== undefined) activity.startTime = updatedActivity.startTime;
        if (updatedActivity.endTime !== undefined) activity.endTime = updatedActivity.endTime;
        if (updatedActivity.location !== undefined) activity.location = updatedActivity.location;
        if (updatedActivity.type !== undefined) activity.type = updatedActivity.type;
        if (updatedActivity.completed !== undefined) activity.completed = updatedActivity.completed;
        return true;
      }
    }
    return false;
  }

  // 删除活动
  deleteActivity(tripId: number, activityId: number): boolean {
    const details = SAMPLE_TRIP_DETAILS.getTripDetails(tripId);
    for (const day of details) {
      const activityIndex = day.activities.findIndex(a => a.id === activityId);
      if (activityIndex !== -1) {
        day.activities.splice(activityIndex, 1);
        return true;
      }
    }
    return false;
  }

  // 添加新活动
  addActivity(tripId: number, date: string, activity: Omit<Activity, 'id'>): number {
    const details = SAMPLE_TRIP_DETAILS.getTripDetails(tripId);

    // 查找对应日期的行程
    const targetDay = details.find(day => day.date === date);
    if (!targetDay) {
      console.error(`未找到日期为 ${date} 的行程`);
      return -1;
    }

    // 生成新的活动ID
    const allActivities: Activity[] = [];
    details.forEach(day => {
      allActivities.push(...day.activities);
    });
    const maxId = allActivities.length > 0 ? Math.max(...allActivities.map(a => a.id)) : 0;
    const newId = maxId + 1;

    // 创建新活动
    const newActivity: Activity = {
      id: newId,
      ...activity
    };

    // 添加到对应日期
    targetDay.activities.push(newActivity);

    console.log(`成功添加活动: ${newActivity.title} 到日期 ${date}`);
    return newId;
  }

  // 获取指定日期的行程
  getDailyItinerary(tripId: number, date: string): DailyItinerary | null {
    const details = SAMPLE_TRIP_DETAILS.getTripDetails(tripId);
    return details.find(day => day.date === date) || null;
  }

  // 获取行程的所有日期
  getTripDates(tripId: number): string[] {
    const details = SAMPLE_TRIP_DETAILS.getTripDetails(tripId);
    return details.map(day => day.date);
  }
}
