/**
 * 添加活动页面
 * 用于添加新的活动到指定日期的行程中
 */

import { Activity, ActivityType, TripDataManager } from '../models/TripModel';
import { ActivityForm, ActivityFormData } from '../components/ActivityForm';
import { THEME_COLORS } from '../utils/TripUtils';
import { IconText, IconConstants } from '../components/IconText';
import router from '@ohos.router';

// 路由参数接口
interface RouteParams {
  tripId: number;
  date: string;
  dayTitle?: string;
}

@Entry
@Component
struct AddActivityPage {
  @State tripId: number = 0;
  @State date: string = '';
  @State dayTitle: string = '';
  @State formData: ActivityFormData = {
    title: '',
    description: '',
    location: '',
    type: ActivityType.SIGHTSEEING,
    startTime: '',
    endTime: ''
  };
  @State isSubmitting: boolean = false;
  private tripManager: TripDataManager = new TripDataManager();

  aboutToAppear() {
    console.log('AddActivityPage: aboutToAppear 被调用');

    // 获取路由参数
    const params = router.getParams();
    console.log('AddActivityPage: 路由参数:', JSON.stringify(params));

    if (params && typeof params === 'object') {
      const routeParams = params as RouteParams;
      if (routeParams.tripId !== undefined && routeParams.date !== undefined) {
        this.tripId = routeParams.tripId;
        this.date = routeParams.date;
        this.dayTitle = routeParams.dayTitle || `第${this.getDayNumber()}天`;
        
        console.log(`AddActivityPage: 行程ID: ${this.tripId}, 日期: ${this.date}`);
        
        // 设置默认时间
        this.setDefaultTime();
      }
    } else {
      console.error('AddActivityPage: 未获取到有效的路由参数');
    }
  }

  // 获取天数
  getDayNumber(): number {
    const dates = this.tripManager.getTripDates(this.tripId);
    const index = dates.indexOf(this.date);
    return index >= 0 ? index + 1 : 1;
  }

  // 设置默认时间
  setDefaultTime() {
    const dailyItinerary = this.tripManager.getDailyItinerary(this.tripId, this.date);
    if (dailyItinerary && dailyItinerary.activities.length > 0) {
      // 如果已有活动，设置为最后一个活动结束后的时间
      const lastActivity = dailyItinerary.activities[dailyItinerary.activities.length - 1];
      const lastEndTime = lastActivity.endTime;
      
      // 简单的时间计算，实际项目中应该使用更完善的时间处理
      const [hours, minutes] = lastEndTime.split(':').map(Number);
      const nextHour = hours + 1;
      this.formData.startTime = `${nextHour.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      this.formData.endTime = `${(nextHour + 1).toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    } else {
      // 如果没有活动，设置默认时间
      this.formData.startTime = '09:00';
      this.formData.endTime = '10:00';
    }
  }

  // 处理返回按钮
  handleBack = () => {
    router.back();
  }

  // 处理保存活动
  handleSaveActivity = () => {
    // 验证表单
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting = true;

    try {
      // 创建活动对象
      const newActivity: Omit<Activity, 'id'> = {
        title: this.formData.title,
        description: this.formData.description,
        startTime: this.formData.startTime,
        endTime: this.formData.endTime,
        location: this.formData.location,
        type: this.formData.type,
        completed: false
      };

      // 添加活动
      const activityId = this.tripManager.addActivity(this.tripId, this.date, newActivity);
      
      if (activityId > 0) {
        console.log(`AddActivityPage: 成功添加活动，ID: ${activityId}`);
        
        // 显示成功提示
        // TODO: 添加Toast提示
        
        // 返回上一页
        router.back();
      } else {
        console.error('AddActivityPage: 添加活动失败');
        // TODO: 显示错误提示
      }
    } catch (error) {
      console.error('AddActivityPage: 添加活动时发生错误:', error);
      // TODO: 显示错误提示
    } finally {
      this.isSubmitting = false;
    }
  }

  // 验证表单
  validateForm(): boolean {
    if (!this.formData.title.trim()) {
      console.error('活动标题不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.startTime) {
      console.error('开始时间不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.endTime) {
      console.error('结束时间不能为空');
      // TODO: 显示错误提示
      return false;
    }

    return true;
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button() {
          IconText({
            iconText: IconConstants.BACK,
            fontSize: 18,
            fontColor: THEME_COLORS.textPrimary,
            iconWidth: 18,
            iconHeight: 18
          })
        }
        .type(ButtonType.Circle)
        .backgroundColor(Color.Transparent)
        .onClick(this.handleBack)

        Blank()

        Text('添加活动')
          .fontSize(18)
          .fontWeight(600)
          .fontColor(THEME_COLORS.textPrimary)

        Blank()

        // 占位，保持标题居中
        Button()
          .type(ButtonType.Circle)
          .backgroundColor(Color.Transparent)
          .visibility(Visibility.Hidden)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(THEME_COLORS.cardBackground)

      Scroll() {
        Column() {
          // 日期信息卡片
          Column() {
            Row() {
              Text('📅')
                .fontSize(24)
                .margin({ right: 12 })

              Column() {
                Text(this.dayTitle)
                  .fontSize(16)
                  .fontWeight(600)
                  .fontColor(THEME_COLORS.textPrimary)
                  .alignSelf(ItemAlign.Start)

                Text(this.date.replace(/-/g, '/'))
                  .fontSize(14)
                  .fontColor(THEME_COLORS.textSecondary)
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 2 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)

              Text('新活动')
                .fontSize(12)
                .fontColor(THEME_COLORS.primary)
                .backgroundColor('#E8F5E8')
                .borderRadius(12)
                .padding({ left: 8, right: 8, top: 4, bottom: 4 })
            }
            .width('100%')
            .alignItems(VerticalAlign.Center)
          }
          .width('100%')
          .backgroundColor(THEME_COLORS.cardBackground)
          .borderRadius(12)
          .padding(16)
          .margin({ top: 16, bottom: 16 })

          // 活动表单
          ActivityForm({ formData: $formData })
        }
        .width('100%')
      }
      .layoutWeight(1)

      // 底部操作按钮
      Row() {
        Button('取消')
          .type(ButtonType.Normal)
          .backgroundColor('#F5F5F5')
          .fontColor(THEME_COLORS.textSecondary)
          .borderRadius(12)
          .layoutWeight(1)
          .height(48)
          .onClick(this.handleBack)

        Button(this.isSubmitting ? '保存中...' : '保存活动')
          .type(ButtonType.Normal)
          .backgroundColor(THEME_COLORS.primary)
          .fontColor(Color.White)
          .borderRadius(12)
          .layoutWeight(1)
          .height(48)
          .margin({ left: 12 })
          .enabled(!this.isSubmitting)
          .onClick(this.handleSaveActivity)
      }
      .width('100%')
      .padding({ left: 16, right: 16, bottom: 16 })
      .backgroundColor(THEME_COLORS.cardBackground)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F8F9FA')
  }
}
