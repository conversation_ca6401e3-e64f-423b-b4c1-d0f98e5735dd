/**
 * 活动详情页面
 * 显示单个活动的详细信息，包括时间、地点、状态等
 */

import { Activity, ActivityType, DailyItinerary, TripDataManager, ActivityQueryResult } from '../models/TripModel';
import { 
  THEME_COLORS, 
  getActivityTypeLabel, 
  getActivityStatusLabel, 
  getActivityTypeIcon 
} from '../utils/TripUtils';
import { IconText, IconConstants } from '../components/IconText';
import router from '@ohos.router';

// 路由参数接口
interface RouteParams {
  tripId: number;
  activityId: number;
  date?: string;
}

@Entry
@Component
struct ActivityDetailPage {
  @State activity: Activity | null = null;
  @State tripId: number = 0;
  @State dailyItinerary: DailyItinerary | null = null;
  @State isCompleted: boolean = false;
  private tripManager: TripDataManager = new TripDataManager();

  aboutToAppear() {
    console.log('ActivityDetailPage: aboutToAppear 被调用');

    // 获取路由参数
    const params = router.getParams();
    console.log('ActivityDetailPage: 路由参数:', JSON.stringify(params));

    if (params && typeof params === 'object') {
      const routeParams = params as RouteParams;
      if (routeParams.tripId !== undefined && routeParams.activityId !== undefined) {
        this.tripId = routeParams.tripId;
        const activityId = routeParams.activityId;
        
        console.log(`ActivityDetailPage: 获取活动ID: ${activityId}, 行程ID: ${this.tripId}`);
        
        // 从行程详情中查找活动
        const tripDetails = this.tripManager.getTripDetails(this.tripId);
        for (const day of tripDetails) {
          const foundActivity = day.activities.find(a => a.id === activityId);
          if (foundActivity) {
            this.activity = foundActivity;
            this.dailyItinerary = day;
            this.isCompleted = foundActivity.completed;
            console.log(`ActivityDetailPage: 找到活动: ${foundActivity.title}`);
            break;
          }
        }

        if (!this.activity) {
          console.error(`ActivityDetailPage: 未找到ID为 ${activityId} 的活动`);
        }
      }
    } else {
      console.error('ActivityDetailPage: 未获取到有效的路由参数');
    }
  }

  // 处理返回按钮
  handleBack = () => {
    router.back();
  }

  // 处理标记完成/未完成
  handleToggleComplete = () => {
    if (!this.activity) return;
    
    const newStatus = !this.isCompleted;
    this.isCompleted = newStatus;
    
    // 更新数据
    this.tripManager.updateActivityStatus(this.tripId, this.activity.id, newStatus);
    this.activity.completed = newStatus;
    
    console.log(`活动 ${this.activity.title} 状态更新为: ${newStatus ? '已完成' : '未完成'}`);
  }

  // 处理编辑活动
  handleEditActivity = () => {
    console.log('编辑活动');
    // TODO: 跳转到编辑活动页面
  }

  // 处理更多操作
  handleMoreActions = () => {
    console.log('更多操作');
    // TODO: 显示更多操作菜单
  }

  // 格式化时间显示
  formatTimeRange(startTime: string, endTime: string): string {
    return `${startTime} - ${endTime}`;
  }

  // 计算活动时长
  calculateDuration(startTime: string, endTime: string): string {
    // 简单的时长计算，假设时间格式为 HH:mm
    const start = startTime.split(':');
    const end = endTime.split(':');
    const startMinutes = parseInt(start[0]) * 60 + parseInt(start[1]);
    const endMinutes = parseInt(end[0]) * 60 + parseInt(end[1]);
    const duration = endMinutes - startMinutes;
    
    if (duration >= 60) {
      const hours = Math.floor(duration / 60);
      const minutes = duration % 60;
      return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
    } else {
      return `${duration}分钟`;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button() {
          IconText({
            iconText: IconConstants.BACK,
            fontSize: 18,
            fontColor: THEME_COLORS.textPrimary,
            iconWidth: 18,
            iconHeight: 18
          })
        }
        .type(ButtonType.Circle)
        .backgroundColor(Color.Transparent)
        .onClick(this.handleBack)

        Blank()

        Text(this.activity?.title || '活动详情')
          .fontSize(18)
          .fontWeight(600)
          .fontColor(THEME_COLORS.textPrimary)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        Blank()

        // 占位，保持标题居中
        Button()
          .type(ButtonType.Circle)
          .backgroundColor(Color.Transparent)
          .visibility(Visibility.Hidden)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor(THEME_COLORS.cardBackground)

      if (this.activity) {
        Scroll() {
          Column() {
            // 活动信息卡片
            Column() {
              // 活动图标和标题
              Row() {
                Text(getActivityTypeIcon(this.activity.type))
                  .fontSize(32)
                  .margin({ right: 16 })

                Column() {
                  Text(this.activity.title)
                    .fontSize(20)
                    .fontWeight(600)
                    .fontColor(Color.White)
                    .alignSelf(ItemAlign.Start)
                    .maxLines(2)
                    .textOverflow({ overflow: TextOverflow.Ellipsis })

                  if (this.activity.description) {
                    Text(this.activity.description)
                      .fontSize(14)
                      .fontColor('rgba(255, 255, 255, 0.8)')
                      .alignSelf(ItemAlign.Start)
                      .margin({ top: 4 })
                      .maxLines(2)
                      .textOverflow({ overflow: TextOverflow.Ellipsis })
                  }
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)

                // 编辑按钮
                Button() {
                  IconText({
                    iconText: '✏️',
                    fontSize: 16,
                    fontColor: '#FFFFFF',
                    iconWidth: 16,
                    iconHeight: 16
                  })
                }
                .type(ButtonType.Circle)
                .backgroundColor('rgba(255, 255, 255, 0.2)')
                .width(32)
                .height(32)
                .onClick(this.handleEditActivity)
              }
              .width('100%')
              .alignItems(VerticalAlign.Top)
            }
            .width('100%')
            .padding(20)
            .backgroundColor(THEME_COLORS.primary)
            .borderRadius(16)
            .margin({ left: 16, right: 16, top: 16, bottom: 16 })

            // 活动信息
            Column() {
              Text('活动信息')
                .fontSize(18)
                .fontWeight(600)
                .fontColor(THEME_COLORS.textPrimary)
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 16 })

              // 信息项列表
              Column() {
                // 地点
                if (this.activity.location) {
                  this.buildInfoItem('地点', this.activity.location, '📍')
                }

                // 时间
                this.buildInfoItem('时间', this.formatTimeRange(this.activity.startTime, this.activity.endTime), '🕐')

                // 时长
                this.buildInfoItem('时长', this.calculateDuration(this.activity.startTime, this.activity.endTime), '⏱️')

                // 类型
                this.buildInfoItem('类型', getActivityTypeLabel(this.activity.type), '🏷️')

                // 状态
                this.buildInfoItem('状态', getActivityStatusLabel(this.activity.completed), '📊')
              }
              .width('100%')
            }
            .width('100%')
            .padding(20)
            .backgroundColor(THEME_COLORS.cardBackground)
            .borderRadius(12)
            .margin({ left: 16, right: 16, bottom: 16 })

            // 标记完成按钮
            Button() {
              Row() {
                Text(this.isCompleted ? '✓' : '○')
                  .fontSize(16)
                  .fontColor(Color.White)
                  .margin({ right: 8 })

                Text(this.isCompleted ? '标记为未完成' : '标记为完成')
                  .fontSize(16)
                  .fontColor(Color.White)
              }
              .alignItems(VerticalAlign.Center)
            }
            .type(ButtonType.Normal)
            .backgroundColor(this.isCompleted ? THEME_COLORS.secondary : THEME_COLORS.success)
            .borderRadius(12)
            .width('calc(100% - 32vp)')
            .height(48)
            .margin({ left: 16, right: 16, bottom: 16 })
            .onClick(this.handleToggleComplete)

            // 操作按钮
            Row() {
              Button('编辑活动')
                .type(ButtonType.Normal)
                .backgroundColor(THEME_COLORS.primaryLight)
                .fontColor(THEME_COLORS.primary)
                .borderRadius(8)
                .layoutWeight(1)
                .height(40)
                .onClick(this.handleEditActivity)

              Button('更多操作')
                .type(ButtonType.Normal)
                .backgroundColor(THEME_COLORS.primaryLight)
                .fontColor(THEME_COLORS.primary)
                .borderRadius(8)
                .layoutWeight(1)
                .height(40)
                .margin({ left: 12 })
                .onClick(this.handleMoreActions)
            }
            .width('calc(100% - 32vp)')
            .margin({ left: 16, right: 16, bottom: 16 })

            // 所属日程
            if (this.dailyItinerary) {
              Column() {
                Text('所属日程')
                  .fontSize(18)
                  .fontWeight(600)
                  .fontColor(THEME_COLORS.textPrimary)
                  .alignSelf(ItemAlign.Start)
                  .margin({ bottom: 12 })

                Row() {
                  Text('1')
                    .fontSize(16)
                    .fontWeight(600)
                    .fontColor(Color.White)
                    .width(24)
                    .height(24)
                    .textAlign(TextAlign.Center)
                    .backgroundColor(THEME_COLORS.primary)
                    .borderRadius(12)

                  Column() {
                    Text(this.dailyItinerary.title)
                      .fontSize(16)
                      .fontWeight(500)
                      .fontColor(THEME_COLORS.textPrimary)
                      .alignSelf(ItemAlign.Start)

                    Text(`${this.dailyItinerary.date} · ${this.dailyItinerary.activities.length}个活动`)
                      .fontSize(14)
                      .fontColor(THEME_COLORS.textSecondary)
                      .alignSelf(ItemAlign.Start)
                      .margin({ top: 2 })
                  }
                  .layoutWeight(1)
                  .alignItems(HorizontalAlign.Start)
                  .margin({ left: 12 })
                }
                .width('100%')
                .padding(12)
                .backgroundColor('#F8F9FA')
                .borderRadius(8)
              }
              .width('100%')
              .padding(20)
              .backgroundColor(THEME_COLORS.cardBackground)
              .borderRadius(12)
              .margin({ left: 16, right: 16, bottom: 16 })
            }
          }
        }
        .layoutWeight(1)
        .scrollBar(BarState.Off)
      } else {
        // 错误状态
        Column() {
          Text('活动信息加载失败')
            .fontSize(16)
            .fontColor(THEME_COLORS.textSecondary)
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(THEME_COLORS.background)
  }

  // 构建信息项
  @Builder buildInfoItem(label: string, value: string, icon: string) {
    Row() {
      Text(icon)
        .fontSize(16)
        .margin({ right: 12 })

      Text(label)
        .fontSize(14)
        .fontColor(THEME_COLORS.textSecondary)
        .width(60)

      Text(value)
        .fontSize(14)
        .fontColor(THEME_COLORS.textPrimary)
        .layoutWeight(1)
        .textAlign(TextAlign.End)
    }
    .width('100%')
    .height(44)
    .alignItems(VerticalAlign.Center)
  }
}
